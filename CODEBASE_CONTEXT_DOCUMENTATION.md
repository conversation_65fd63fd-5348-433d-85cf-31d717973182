# EduAI Platform - Codebase Context Documentation

## 📁 Project Structure Overview

### Core Directories
- `src/` - Main React application source code
- `src/components/` - Reusable UI components
- `src/pages/` - Main application pages/routes
- `src/context/` - React context providers
- `src/utils/` - Utility functions and API integrations
- `backend.py` - External Python Flask API (OCR, Document Generation, Grading)

## 🔐 Authentication System

### Current Implementation
**Files**: `src/context/AuthContext.tsx`, `src/pages/Login.tsx`, `src/utils/supabase.ts`

**Current Flow**:
- Separate signup/signin for teachers and students
- Role selection during signup (`teacher` | `student`)
- Role-based access control throughout app
- Supabase Auth integration with user profiles

**Key Functions**:
- `signUp(email, password, name, role)` - Creates user with specific role
- `signIn(email, password)` - Authenticates user
- `getUserProfile()` - Fetches user profile with role
- Role checking: `isTeacher()`, `isStudent()`

## 👥 User Management

### Database Schema
**Table**: `users`
- `id` (UUID, Primary Key)
- `name` (Text)
- `email` (Text, Unique)
- `role` ('teacher' | 'student')
- `avatar_url` (Text, Optional)
- `created_at` (Timestamp)

### Current Role System
- **Teachers**: Can create classes, assignments, grade submissions
- **Students**: Can join classes, submit assignments, view grades

## 🏫 Class Management

### Files
- `src/pages/Classes.tsx` - Class listing and management
- `src/pages/CreateClass.tsx` - Class creation form
- `src/pages/JoinClass.tsx` - Class joining interface
- `src/pages/ClassDetail.tsx` - Individual class view
- `src/pages/ClassStudents.tsx` - Class member management

### Database Schema
**Table**: `classes`
- `id` (UUID, Primary Key)
- `name` (Text)
- `description` (Text)
- `class_code` (Text, Unique)
- `teacher_id` (UUID, Foreign Key to users)
- `created_at` (Timestamp)

**Table**: `class_members`
- `id` (UUID, Primary Key)
- `class_id` (UUID, Foreign Key to classes)
- `user_id` (UUID, Foreign Key to users)
- `joined_at` (Timestamp)

### Key Features
- Teachers create classes with unique codes
- Students join using class codes
- Real-time member management
- Class-specific dashboards

## 📝 Assignment System

### Files
- `src/pages/CreateAssignment.tsx` - Assignment creation (Teachers)
- `src/pages/AssignmentDetail.tsx` - Assignment viewing
- `src/pages/SubmitAssignment.tsx` - Assignment submission (Students)
- `src/pages/ReviewSubmission.tsx` - Submission review (Teachers)

### Database Schema
**Table**: `assignments`
- `id` (UUID, Primary Key)
- `class_id` (UUID, Foreign Key to classes)
- `title` (Text)
- `content` (Text)
- `max_marks` (Integer)
- `due_date` (Timestamp)
- `created_by` (UUID, Foreign Key to users)
- `ai_prompt` (Text, Optional)
- `created_at` (Timestamp)

**Table**: `submissions`
- `id` (UUID, Primary Key)
- `assignment_id` (UUID, Foreign Key to assignments)
- `student_id` (UUID, Foreign Key to users)
- `file_url` (Text)
- `file_name` (Text)
- `ocr_text` (Text, Optional)
- `grade` (Integer, Optional)
- `feedback` (Text, Optional)
- `submitted_at` (Timestamp)
- `graded_at` (Timestamp, Optional)
- `graded_by` (Text, Optional)

## 🤖 AI Integration & Processing

### OCR Processing
**Files**: `src/utils/api.ts`, `src/pages/SubmitAssignment.tsx`

**Current Flow**:
1. Student uploads file via `FileUpload` component
2. File stored in Cloudinary
3. Manual OCR trigger via "Extract Text with OCR" button
4. Calls `extractText(file)` API function
5. Backend processes with Mistral OCR (fallback to Tesseract)
6. Extracted text stored in submission

### Automatic Grading
**Files**: `src/utils/api.ts`, `src/pages/SubmitAssignment.tsx`

**Current Flow**:
1. After submission creation, automatic grading triggers
2. Calls `gradeSubmission()` with OCR text and assignment content
3. Backend uses Groq AI for grading
4. Grade and feedback stored in submission
5. Notification sent to student

### Backend API (backend.py)
**Base URL**: `https://fyp-backend-xah8.onrender.com`

**Endpoints**:
- `POST /api/ocr/extract` - OCR text extraction
- `POST /api/generate/document` - AI document generation
- `POST /api/grade/submission` - Automatic grading
- `GET /health` - Health check

## 🎯 Key Features Working Correctly

### Teacher Functionality ✅
- Class creation and management
- Assignment creation (manual and AI-generated)
- Student submission review
- Manual grading and feedback
- Class member management

### Student Functionality ✅
- Class joining with codes
- Assignment viewing
- File upload and submission
- OCR text extraction
- Automatic grading reception
- Grade dispute tickets

### File Management ✅
- Cloudinary integration for file storage
- Support for PDF, PNG, JPG, JPEG, AVIF
- File validation and error handling
- Progress tracking during upload

## 🔄 Current Issues to Address

### Authentication Restructuring Needed
1. **Remove role-based signup** - Single signup for all users
2. **Dynamic role assignment** - Users can be both teacher and student
3. **Context-based permissions** - Role determined by class relationship
4. **Automatic OCR trigger** - Remove manual OCR button
5. **Seamless grading flow** - Auto-trigger after OCR completion

### Files Requiring Major Changes
- `src/context/AuthContext.tsx` - Remove role-based logic
- `src/pages/Login.tsx` - Simplify signup form
- `src/utils/supabase.ts` - Update user management functions
- `src/pages/SubmitAssignment.tsx` - Auto-trigger OCR and grading
- Database schema - Update user table structure

## 🎯 Target Architecture

### New User Flow
1. User signs up with email/username/password (no role selection)
2. User can create classes (becomes teacher for those classes)
3. User can join classes (becomes student for those classes)
4. Permissions determined by class relationship, not global role
5. Automatic OCR and grading on submission

### New Submission Flow
1. Student uploads file
2. OCR automatically triggers on upload completion
3. Grading automatically triggers after OCR completion
4. Student receives notification with grade
5. No manual intervention required
