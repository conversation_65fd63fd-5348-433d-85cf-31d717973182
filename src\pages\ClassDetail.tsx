import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { PlusIcon, FileTextIcon, CalendarIcon, UsersIcon, UserPlusIcon } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { getClassAssignments, getClassDetails } from '../utils/supabase';
import GradesTable from '../components/grades/GradesTable';
import BackButton from '../components/BackButton';
interface ClassInfo {
  name: string;
  color: string;
  students: number;
  class_code: string;
  description?: string;
}
const ClassDetail = () => {
  const {
    classId
  } = useParams();
  const {
    user,
    isTeacherForClass
  } = useAuth();
  const [activeTab, setActiveTab] = useState('assignments');
  const [assignments, setAssignments] = useState([]);
  const [grades, setGrades] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [classInfo, setClassInfo] = useState<ClassInfo | null>(null);
  const [classMembers, setClassMembers] = useState<any[]>([]);
  const [isTeacher, setIsTeacher] = useState(false);

  useEffect(() => {
    const fetchClassData = async () => {
      setIsLoading(true);
      try {
        if (!classId) return;

        // Fetch class details
        const { data: classData, error: classError } = await getClassDetails(classId);
        if (classError) {
          throw classError;
        }

        if (classData) {
          setClassInfo({
            name: classData.name,
            color: classData.color_scheme || '#3B82F6',
            students: classData.class_members?.length || 0,
            class_code: classData.class_code,
            description: classData.description
          });

          // Set class members data
          setClassMembers(classData.class_members || []);
        }

        // Fetch assignments for this class
        const { data: assignmentData, error: assignmentError } = await getClassAssignments(classId);
        if (assignmentError) {
          console.error('Error fetching assignments:', assignmentError);
        } else {
          setAssignments(assignmentData || []);
        }

        // For now, we'll skip grades until we implement the grading system
        if (isTeacher()) {
          setGrades([]); // Will be implemented later
        }
      } catch (error) {
        console.error('Error fetching class data:', error);
      } finally {
        setIsLoading(false);
      }
    };
    if (classId) {
      fetchClassData();
    }
  }, [classId, isTeacher]);
  if (isLoading || !classInfo) {
    return <div className="max-w-7xl mx-auto">
        <div className="mb-4">
          <BackButton to="/classes" />
        </div>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>;
  }

  return <div className="max-w-7xl mx-auto">
      <div className="mb-4">
        <BackButton to="/classes" />
      </div>
      <div className="bg-blue-600 text-white p-8 rounded-lg mb-6" style={{ backgroundColor: classInfo.color }}>
        <h1 className="text-3xl font-bold mb-2">{classInfo.name}</h1>
        {classInfo.description && (
          <p className="text-white/90 mb-4">{classInfo.description}</p>
        )}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <UsersIcon className="h-5 w-5 mr-2" />
            <span>{classInfo.students} students</span>
          </div>
          {isTeacher() && (
            <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2">
              <div className="text-sm text-white/80">Class Code</div>
              <div className="text-lg font-mono font-bold">{classInfo.class_code}</div>
            </div>
          )}
        </div>
      </div>
      <div className="bg-white border border-gray-200 rounded-lg mb-6">
        <div className="flex border-b border-gray-200">
          <button className={`px-6 py-4 font-medium ${activeTab === 'assignments' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-blue-600'}`} onClick={() => setActiveTab('assignments')}>
            Assignments
          </button>
          {isTeacher() && <button className={`px-6 py-4 font-medium ${activeTab === 'grades' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-blue-600'}`} onClick={() => setActiveTab('grades')}>
              Grades
            </button>}
          <button className={`px-6 py-4 font-medium ${activeTab === 'people' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-blue-600'}`} onClick={() => setActiveTab('people')}>
            People
          </button>
        </div>
        <div className="p-6">
          {activeTab === 'assignments' && <div>
              {isTeacher() && <div className="mb-6">
                  <Link to={`/classes/${classId}/create-assignment`} className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center hover:bg-blue-700 w-fit">
                    <PlusIcon className="h-5 w-5 mr-2" />
                    Create Assignment
                  </Link>
                </div>}
              {assignments.length > 0 ? <div className="space-y-4">
                  {assignments.map((assignment: any) => <Link key={assignment.id} to={`/classes/${classId}/assignments/${assignment.id}`} className="block bg-white border border-gray-200 rounded-lg p-4 hover:border-blue-500 transition">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start">
                          <FileTextIcon className="h-6 w-6 text-blue-600 mr-3 mt-1" />
                          <div>
                            <h3 className="font-medium">{assignment.title}</h3>
                            <p className="text-sm text-gray-500">
                              Max marks: {assignment.max_marks}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center text-gray-500">
                            <CalendarIcon className="h-4 w-4 mr-1" />
                            <span className="text-sm">
                              Due{' '}
                              {new Date(assignment.due_date).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>
                    </Link>)}
                </div> : <div className="text-center py-8 text-gray-500">
                  <FileTextIcon className="h-12 w-12 mx-auto mb-3 text-gray-400" />
                  <h3 className="text-lg font-medium mb-1">
                    No assignments yet
                  </h3>
                  <p>
                    {isTeacher() ? 'Create your first assignment to get started' : "Your teacher hasn't created any assignments yet"}
                  </p>
                </div>}
            </div>}
          {activeTab === 'grades' && isTeacher() && <GradesTable grades={grades} onUpdateGrade={(id, marks) => {
          setGrades(grades.map((g: any) => g.id === id ? {
            ...g,
            marks
          } : g));
        }} />}
          {activeTab === 'people' && <div>
              {isTeacher() && (
                <div className="mb-6 flex items-center justify-between">
                  <Link to={`/classes/${classId}/students`} className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center hover:bg-blue-700 w-fit">
                    <UserPlusIcon className="h-5 w-5 mr-2" />
                    Manage Students
                  </Link>
                  {classInfo && (
                    <div className="bg-gray-100 px-4 py-2 rounded-lg">
                      <span className="text-sm text-gray-600">Class Code: </span>
                      <span className="font-mono font-bold text-lg">{classInfo.class_code}</span>
                    </div>
                  )}
                </div>
              )}

              <div className="divide-y divide-gray-200 border border-gray-200 rounded-lg">
                {/* Teachers Section */}
                <div className="p-4 bg-gray-50">
                  <h3 className="font-medium">Teachers</h3>
                </div>
                <div className="p-4">
                  {classMembers.filter(member => member.role === 'teacher').length > 0 ? (
                    <div className="space-y-3">
                      {classMembers.filter(member => member.role === 'teacher').map((teacher) => (
                        <div key={teacher.id} className="flex items-center">
                          <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                            <span className="text-blue-600 font-medium">
                              {teacher.users.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div>
                            <p className="font-medium">{teacher.users.name}</p>
                            <p className="text-sm text-gray-500">{teacher.users.email}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-sm">No teachers found</p>
                  )}
                </div>

                {/* Students Section */}
                <div className="p-4 bg-gray-50">
                  <h3 className="font-medium">Students</h3>
                </div>
                <div className="p-4">
                  {classMembers.filter(member => member.role === 'student').length > 0 ? (
                    <div className="space-y-3">
                      {classMembers.filter(member => member.role === 'student').map((student) => (
                        <div key={student.id} className="flex items-center">
                          <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                            <span className="text-gray-600 font-medium">
                              {student.users.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div>
                            <p className="font-medium">{student.users.name}</p>
                            <p className="text-sm text-gray-500">{student.users.email}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-sm">No students have joined this class yet</p>
                  )}
                </div>
              </div>
            </div>}
        </div>
      </div>
    </div>;
};
export default ClassDetail;